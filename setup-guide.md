# Gemini CRM Customer Service AI Agent - Setup Guide

## Overview
This n8n workflow creates an intelligent customer service AI agent for Gemini CRM Women Bags Store with Egyptian dialect support, image analysis capabilities, and Telegram integration.

## Prerequisites

### 1. n8n Installation
- n8n version 1.0+ with LangChain nodes support
- Access to n8n instance with admin privileges

### 2. Required API Keys and Credentials
- **Telegram Bot Token**: Create via @BotFather on Telegram
- **OpenAI API Key**: For image analysis and Egyptian dialect processing
- **Gemini CRM API**: Access token and base URL for your CRM system

### 3. Environment Variables
Set these in your n8n environment:
```bash
CRM_API_BASE_URL=https://your-crm-api.com/api/v1
CRM_API_TOKEN=your_crm_api_token
N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true
```

## Installation Steps

### Step 1: Import Workflow
1. Copy the content from `gemini-crm-customer-service-workflow.json`
2. In n8n, go to **Workflows** → **Import from JSON**
3. Paste the JSON content and click **Import**

### Step 2: Configure Credentials

#### Telegram Bot API
1. Go to **Settings** → **Credentials** → **Add Credential**
2. Select **Telegram API**
3. Enter your bot token from @BotFather
4. Test the connection
5. Save as "telegram-bot-credentials"

#### OpenAI API
1. Add new credential for **OpenAI API**
2. Enter your OpenAI API key
3. Test the connection
4. Save as "openai-credentials"

### Step 3: Set Environment Variables
1. Go to **Settings** → **Environment Variables**
2. Add the following variables:
   - `CRM_API_BASE_URL`: Your CRM API base URL
   - `CRM_API_TOKEN`: Your CRM API authentication token

### Step 4: Configure Telegram Bot
1. Set webhook URL in your Telegram bot:
   ```
   https://your-n8n-instance.com/webhook/telegram-bot
   ```
2. Enable image downloads in bot settings
3. Configure bot commands and description

## Node Configuration Details

### 1. Telegram Trigger
- **Updates**: message, edited_message
- **Download**: true (for image handling)
- **Image Size**: large (for better analysis)

### 2. Message Router (Switch Node)
Routes messages based on content type:
- **Has Image**: Routes to Image Analyzer
- **Text Only**: Routes to Text Processor
- **Fallback**: Routes to Error Handler

### 3. Image Analyzer (OpenAI Node)
- **Model**: gpt-4-vision-preview
- **Resource**: image
- **Operation**: analyze
- **Prompt**: Egyptian dialect instructions for bag analysis
- **Detail Level**: high
- **Max Tokens**: 500

### 4. Text Processor (OpenAI Node)
- **Model**: gpt-4
- **Resource**: text
- **Operation**: message
- **System Message**: Egyptian dialect customer service instructions
- **Temperature**: 0.7
- **Max Tokens**: 800

### 5. AI Agent
- **Type**: conversationalAgent
- **System Message**: Egyptian dialect customer service persona
- **Max Iterations**: 5
- **Tools**: Connected to CRM APIs and product search

## Egyptian Dialect Configuration

The workflow is specifically configured for Egyptian Arabic dialect with:

### System Prompts
- Customer service responses in Egyptian colloquial Arabic
- Friendly and professional tone
- Cultural context awareness
- Local expressions and terminology

### Example Phrases
- "أهلاً وسهلاً" (Welcome)
- "إزيك؟" (How are you?)
- "عايزة إيه النهاردة؟" (What do you need today?)
- "الحقيبة دي جميلة قوي" (This bag is very beautiful)

## CRM API Integration

### Required Endpoints
Your CRM system should provide these endpoints:

#### 1. Product Search
```
POST /products/search
{
  "query": "bag description from image analysis",
  "category": "women_bags",
  "limit": 5
}
```

#### 2. Customer Lookup
```
GET /customers/lookup?telegram_id=123456789
```

#### 3. Conversation Logging
```
POST /conversations/log
{
  "customer_id": "customer_id",
  "telegram_id": 123456789,
  "message_type": "image|text",
  "user_message": "customer message",
  "bot_response": "ai response",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Testing Procedures

### 1. Text Message Testing
Send these test messages to your bot:
- "عايزة حقيبة يد صغيرة" (I want a small handbag)
- "عندكم إيه في الحقائب الجلد؟" (What leather bags do you have?)
- "أسعار الحقائب إيه؟" (What are the bag prices?)

### 2. Image Analysis Testing
Send images of bags with captions:
- Photo of handbag + "دي زي إيه؟" (What's this like?)
- Photo of bag + "عندكم زي كده؟" (Do you have something like this?)

### 3. Error Handling Testing
- Send unsupported file types
- Send very long messages
- Test with network interruptions

## Monitoring and Maintenance

### 1. Execution Monitoring
- Check workflow execution logs regularly
- Monitor API rate limits (OpenAI, Telegram)
- Track response times and success rates

### 2. Performance Optimization
- Adjust OpenAI model parameters based on usage
- Optimize image analysis detail levels
- Fine-tune Egyptian dialect responses

### 3. Regular Updates
- Update product catalog integration
- Refresh Egyptian dialect phrases
- Monitor customer feedback and adjust responses

## Troubleshooting

### Common Issues

#### 1. Telegram Webhook Not Working
- Check webhook URL configuration
- Verify SSL certificate
- Test webhook endpoint manually

#### 2. OpenAI API Errors
- Verify API key validity
- Check rate limits and quotas
- Monitor token usage

#### 3. CRM API Integration Issues
- Test API endpoints manually
- Check authentication tokens
- Verify API response formats

#### 4. Egyptian Dialect Issues
- Review and update system prompts
- Test with native speakers
- Adjust cultural context as needed

### Error Messages
- **"عذراً، حدث خطأ تقني"**: Technical error occurred
- **"مش فاهم الرسالة دي"**: Message not understood
- **"جرب تاني من فضلك"**: Please try again

## Security Considerations

### 1. API Key Protection
- Store all API keys in n8n credentials
- Never expose keys in workflow JSON
- Rotate keys regularly

### 2. Data Privacy
- Log only necessary conversation data
- Implement data retention policies
- Comply with privacy regulations

### 3. Access Control
- Restrict workflow edit permissions
- Monitor execution logs
- Implement rate limiting

## Support and Maintenance

For ongoing support:
1. Monitor workflow execution logs
2. Track customer satisfaction metrics
3. Regular testing of all features
4. Update Egyptian dialect responses based on feedback
5. Maintain CRM API integration compatibility

## Next Steps

After successful deployment:
1. Train customer service team on the system
2. Gather customer feedback
3. Implement additional features based on usage patterns
4. Scale infrastructure as needed
5. Consider adding voice message support