{"name": "Gemini CRM Customer Service AI Agent", "nodes": [{"id": "telegram-trigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [240, 300], "parameters": {"updates": ["message", "edited_message"], "additionalFields": {"download": true, "imageSize": "large"}}, "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Telegram Bot API"}}}, {"id": "message-router", "name": "Message Router", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [460, 300], "parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-image", "leftValue": "={{ $json.message.photo }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty"}}, {"id": "text-only", "leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists"}}], "combinator": "and"}, "fallbackOutput": "extra"}}, {"id": "image-analyzer", "name": "Image Analyzer", "type": "n8n-nodes-base.openAi", "typeVersion": 1.8, "position": [680, 200], "parameters": {"resource": "image", "operation": "analyze", "modelId": {"mode": "list", "value": "gpt-4-vision-preview"}, "text": "أنت خبير في تحليل الصور للحقائب النسائية. قم بتحليل هذه الصورة وحدد:\n1. نوع الحقيبة (حقيبة يد، حقيبة كتف، محفظة، إلخ)\n2. اللون الأساسي والألوان الثانوية\n3. المادة المصنوعة منها (جلد، قماش، إلخ)\n4. الحجم التقريبي (صغير، متوسط، كبير)\n5. أي تفاصيل مميزة أو زخارف\n6. الحالة العامة للحقيبة\n\nأجب باللهجة المصرية العامية بطريقة ودودة ومفيدة.", "inputType": "base64", "binaryPropertyName": "data", "options": {"detail": "high", "maxTokens": 500}}, "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"id": "product-matcher", "name": "Product Matcher", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 200], "parameters": {"url": "{{ $vars.CRM_API_BASE_URL }}/products/search", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.CRM_API_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $json.choices[0].message.content }}"}, {"name": "category", "value": "women_bags"}, {"name": "limit", "value": 5}]}}}, {"id": "text-processor", "name": "Text Processor", "type": "n8n-nodes-base.openAi", "typeVersion": 1.8, "position": [680, 400], "parameters": {"resource": "text", "operation": "message", "modelId": {"mode": "list", "value": "gpt-4"}, "messages": {"values": [{"role": "system", "content": "أنت مساعد خدمة عملاء لمتجر جيميني للحقائب النسائية. تتحدث باللهجة المصرية العامية بطريقة ودودة ومهنية. مهمتك:\n1. فهم استفسارات العملاء عن الحقائب\n2. تقديم معلومات مفيدة عن المنتجات\n3. مساعدة العملاء في اختيار الحقيبة المناسبة\n4. الرد على الشكاوى والاستفسارات\n5. توجيه العملاء للمنتجات المناسبة\n\nاستخدم اللهجة المصرية العامية في ردودك وكن مفيد ومتفهم."}, {"role": "user", "content": "={{ $json.message.text }}"}]}, "options": {"temperature": 0.7, "maxTokens": 800}}, "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"id": "customer-lookup", "name": "Customer Lookup", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 400], "parameters": {"url": "{{ $vars.CRM_API_BASE_URL }}/customers/lookup", "method": "GET", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.CRM_API_TOKEN }}"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "telegram_id", "value": "={{ $json.message.from.id }}"}]}}}, {"id": "ai-agent", "name": "AI Agent", "type": "n8n-nodes-base.agent", "typeVersion": 1.6, "position": [1120, 300], "parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "أنت مساعد ذكي لخدمة عملاء متجر جيميني للحقائب النسائية. تتحدث باللهجة المصرية العامية وتساعد العملاء في:\n\n1. تحليل صور الحقائب وتحديد مواصفاتها\n2. البحث عن منتجات مشابهة في المتجر\n3. الإجابة على استفسارات العملاء\n4. تقديم توصيات مخصصة\n5. معالجة الشكاوى والطلبات\n\nاستخدم المعلومات المتاحة من قاعدة البيانات والصور لتقديم أفضل خدمة ممكنة. كن ودود ومفيد ومتفهم.", "hasOutputParser": true, "options": {"systemMessage": "أنت مساعد خدمة عملاء محترف لمتجر جيميني للحقائب النسائية. تجيب باللهجة المصرية العامية وتساعد العملاء بطريقة ودودة ومهنية.", "maxIterations": 5, "returnIntermediateSteps": false}}, "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"id": "response-formatter", "name": "Response Formatter", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 300], "parameters": {"assignments": {"assignments": [{"id": "response-text", "name": "response_text", "value": "={{ $json.output }}", "type": "string"}, {"id": "chat-id", "name": "chat_id", "value": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "type": "number"}, {"id": "customer-id", "name": "customer_id", "value": "={{ $('Customer Lookup').item.json.customer_id || null }}", "type": "string"}, {"id": "message-type", "name": "message_type", "value": "={{ $('Telegram Trigger').item.json.message.photo ? 'image' : 'text' }}", "type": "string"}]}, "options": {}}}, {"id": "telegram-response", "name": "Telegram Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 300], "parameters": {"resource": "message", "operation": "sendMessage", "chatId": "={{ $json.chat_id }}", "text": "={{ $json.response_text }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "disable_web_page_preview": true}}, "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Telegram Bot API"}}}, {"id": "conversation-logger", "name": "Conversation Logger", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 300], "parameters": {"url": "{{ $vars.CRM_API_BASE_URL }}/conversations/log", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.CRM_API_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "customer_id", "value": "={{ $json.customer_id }}"}, {"name": "telegram_id", "value": "={{ $('Telegram Trigger').item.json.message.from.id }}"}, {"name": "message_type", "value": "={{ $json.message_type }}"}, {"name": "user_message", "value": "={{ $('Telegram Trigger').item.json.message.text || 'صورة' }}"}, {"name": "bot_response", "value": "={{ $json.response_text }}"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}}}, {"id": "error-handler", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 500], "parameters": {"assignments": {"assignments": [{"id": "error-response", "name": "response_text", "value": "عذراً، حدث خطأ تقني. من فضلك حاول مرة أخرى أو تواصل مع خدمة العملاء مباشرة.", "type": "string"}, {"id": "chat-id", "name": "chat_id", "value": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "type": "number"}]}, "options": {}}}, {"id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 500], "parameters": {"resource": "message", "operation": "sendMessage", "chatId": "={{ $json.chat_id }}", "text": "={{ $json.response_text }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Telegram Bot API"}}}], "connections": {"telegram-trigger": {"main": [[{"node": "message-router", "type": "main", "index": 0}]]}, "message-router": {"main": [[{"node": "image-analyzer", "type": "main", "index": 0}], [{"node": "text-processor", "type": "main", "index": 0}], [{"node": "error-handler", "type": "main", "index": 0}]]}, "image-analyzer": {"main": [[{"node": "product-matcher", "type": "main", "index": 0}]]}, "product-matcher": {"main": [[{"node": "ai-agent", "type": "main", "index": 0}]]}, "text-processor": {"main": [[{"node": "customer-lookup", "type": "main", "index": 0}]]}, "customer-lookup": {"main": [[{"node": "ai-agent", "type": "main", "index": 0}]]}, "ai-agent": {"main": [[{"node": "response-formatter", "type": "main", "index": 0}]]}, "response-formatter": {"main": [[{"node": "telegram-response", "type": "main", "index": 0}]]}, "telegram-response": {"main": [[{"node": "conversation-logger", "type": "main", "index": 0}]]}, "error-handler": {"main": [[{"node": "error-response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "", "timezone": "Africa/Cairo"}}